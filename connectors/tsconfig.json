{"compilerOptions": {"target": "es2017", "lib": ["ES2021"], "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noUncheckedIndexedAccess": true, "baseUrl": ".", "paths": {"@connectors/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.mjs", "./.eslintrc.js"], "exclude": ["node_modules"]}