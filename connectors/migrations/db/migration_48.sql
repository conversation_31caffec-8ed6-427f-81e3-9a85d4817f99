-- Migration created on Jan 22, 2025
ALTER TABLE "confluence_configurations" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "confluence_configurations"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "confluence_pages" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "confluence_pages"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "confluence_spaces" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "confluence_spaces"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "slack_configurations" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "slack_configurations"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "slack_messages" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "slack_messages"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "slack_channels" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "slack_channels"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "slack_chat_bot_messages" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "slack_chat_bot_messages"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "slack_bot_whitelist" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "slack_bot_whitelist"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notion_pages" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "notion_pages"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notion_databases" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "notion_databases"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notion_connector_states" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "notion_connector_states"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "github_connector_states" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "github_connector_states"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "github_issues" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "github_issues"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "github_discussions" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "github_discussions"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "github_code_repositories" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "github_code_repositories"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "github_code_files" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "github_code_files"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "github_code_directories" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "github_code_directories"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "google_drive_folders" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "google_drive_folders"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "google_drive_files" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "google_drive_files"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "google_drive_sheets" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "google_drive_sheets"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "google_drive_sync_tokens" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "google_drive_sync_tokens"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "microsoft_configurations" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "microsoft_configurations"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "microsoft_roots" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "microsoft_roots"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "microsoft_nodes" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "microsoft_nodes"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notion_connector_block_cache_entries" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "notion_connector_block_cache_entries"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notion_connector_page_cache_entries" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "notion_connector_page_cache_entries"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notion_connector_resources_to_check_cache_entries" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "notion_connector_resources_to_check_cache_entries"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "google_drive_configs" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "google_drive_configs"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "intercom_workspaces" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "intercom_workspaces"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "intercom_help_centers" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "intercom_help_centers"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "intercom_collections" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "intercom_collections"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "intercom_articles" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "intercom_articles"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "intercom_teams" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "intercom_teams"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "intercom_conversations" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "intercom_conversations"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "webcrawler_configurations" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "webcrawler_configurations"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "webcrawler_folders" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "webcrawler_folders"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "webcrawler_pages" ALTER COLUMN "connectorId" SET NOT NULL;ALTER TABLE "webcrawler_pages"  ADD FOREIGN KEY ("connectorId") REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- Real new addition.
ALTER TABLE "public"."webcrawler_configuration_headers" ADD COLUMN "connectorId" BIGINT NOT NULL REFERENCES "connectors" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
